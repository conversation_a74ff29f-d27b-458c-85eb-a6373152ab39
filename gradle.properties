# Project-wide Gradle settings.
# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.
# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
# When configured, <PERSON><PERSON><PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true
org.gradle.jvmargs=-Xmx4048m -XX:MaxMetaspaceSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8 -Dorg.aspectj.dump.exception=false
# AspectJ 升级配置 - 如果出现问题可以设置为 false 禁用强制版本
useUpdatedAspectJ=true
# 是否打包APK，打正式包时请设置为true,使用正式的签名
isNeedPackage=false
# 是否使用booster优化APK，这里需要注意gradle的版本，对于最新的gradle版本可能存在兼容问题
isUseBooster=false
android.precompileDependenciesResources=false

android.useAndroidX=true
android.enableJetifier=true
