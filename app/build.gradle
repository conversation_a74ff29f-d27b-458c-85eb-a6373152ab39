repositories {
    google()
    mavenCentral()
}
apply plugin: 'com.android.application'
apply plugin: 'img-optimizer'
//打包时，记得设置true启用
if (isNeedPackage.toBoolean() && isUseBooster.toBoolean()) {
    apply plugin: 'com.didiglobal.booster'
}

// 强制版本解析
configurations.all {
    resolutionStrategy {
        force 'androidx.core:core:1.9.0'
        force 'androidx.core:core-ktx:1.9.0'
        // 使用相对保守的 AspectJ 版本，平衡兼容性和稳定性
        // 如果出现问题，可以在 gradle.properties 中设置 useUpdatedAspectJ=false
        if (project.hasProperty('useUpdatedAspectJ') && useUpdatedAspectJ.toBoolean()) {
            force 'org.aspectj:aspectjrt:1.9.19'
            force 'org.aspectj:aspectjweaver:1.9.19'
            force 'org.aspectj:aspectjtools:1.9.19'
        }
    }
}

android {
    compileSdk 34

    defaultConfig {
        applicationId "cn.geyuantz.reader"
        minSdkVersion 21
        targetSdkVersion build_versions.target_sdk
        versionCode 19
        versionName "2.1.0"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        multiDexEnabled true
        vectorDrawables.useSupportLibrary = true

        javaCompileOptions {
            annotationProcessorOptions {
                arguments = [ moduleName : project.getName() ]
            }
        }
    }

    signingConfigs {
        /*if (isNeedPackage.toBoolean()) {
            release {
                storeFile file(app_release.storeFile)
                storePassword app_release.storePassword
                keyAlias app_release.keyAlias
                keyPassword app_release.keyPassword
            }
        }*/

        debug {
            storeFile file("./debug.jks")
            storePassword "123456"
            keyAlias "debug"
            keyPassword "123456"
        }
    }

    buildTypes {
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            if (isNeedPackage.toBoolean()) {
                signingConfig signingConfigs.release

                Properties properties = new Properties()
                properties.load(project.rootProject.file('local.properties').newDataInputStream())
                def appID = properties.getProperty("APP_ID_UMENG")
                if (appID != null) {
                    buildConfigField "String", "APP_ID_UMENG", appID
                } else {
                    buildConfigField "String", "APP_ID_UMENG", '""'
                }
            } else {
                signingConfig signingConfigs.debug
                buildConfigField "String", "APP_ID_UMENG", '""'
            }
        }

        debug {
            debuggable true
            minifyEnabled false

            signingConfig signingConfigs.debug
            buildConfigField "String", "APP_ID_UMENG", '""'
        }
    }

    lintOptions {
        abortOnError false
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    buildFeatures {
        viewBinding true
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'org.projectlombok:lombok:1.18.30'
    implementation 'androidx.core:core-ktx:1.16.0'
    annotationProcessor 'org.projectlombok:lombok:1.18.30'
    testImplementation deps.junit
    androidTestImplementation deps.runner
    androidTestImplementation deps.espresso.core

    //分包
    implementation deps.androidx.multidex

    implementation 'com.alibaba.android:vlayout:1.3.0'
    //下拉刷新
    implementation  'io.github.scwang90:refresh-layout-kernel:2.1.1'
    implementation  'io.github.scwang90:refresh-header-material:2.1.1'
    //WebView
    implementation 'com.github.xuexiangjys.AgentWeb:agentweb-core:1.0.1'
    implementation 'com.github.xuexiangjys.AgentWeb:agentweb-download:1.0.1'//选填
    //腾讯的键值对存储mmkv
    implementation 'com.tencent:mmkv:1.2.10'
    //屏幕适配AutoSize
    implementation 'me.jessyan:autosize:1.2.1'
    //umeng统计
    implementation 'com.umeng.umsdk:common:9.3.8'
    implementation  'com.umeng.umsdk:asms:1.2.1'
    // 下拉刷新
    implementation 'com.github.xuexiangjys.SmartRefreshLayout:refresh-header:1.1.6'
    implementation 'com.github.xuexiangjys.SmartRefreshLayout:refresh-layout:1.1.6'
    // XQRCode二维码扫描
    implementation 'com.github.xuexiangjys:XQRCode:1.1.1'
    //预加载占位控件
    implementation 'me.samlss:broccoli:1.0.0'

    implementation 'com.zzhoujay.richtext:richtext:3.0.8'
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'

    //ANR异常捕获
    implementation 'com.github.anrwatchdog:anrwatchdog:1.4.0'

    //美团多渠道打包
    implementation 'com.meituan.android.walle:library:1.1.6'
    // 双列表联动
    implementation 'com.kunminx.linkage:linkage-recyclerview:2.7.0'

    // SLF4J日志框架 - 替代旧EPUB阅读器中的依赖
    implementation 'org.slf4j:slf4j-api:1.7.36'
    implementation 'org.slf4j:slf4j-android:1.7.36'

    // Kotlin标准库 - 使用兼容版本
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4"

    // 强制使用兼容的androidx.core版本
    implementation 'androidx.core:core:1.9.0'
    implementation 'androidx.core:core-ktx:1.9.0'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.6.2'

    // Readium toolkit
    implementation "org.readium.kotlin-toolkit:readium-shared:$readium_version"
    implementation "org.readium.kotlin-toolkit:readium-streamer:$readium_version"
    implementation "org.readium.kotlin-toolkit:readium-navigator:$readium_version"
    implementation "org.readium.kotlin-toolkit:readium-opds:$readium_version"
    implementation "org.readium.kotlin-toolkit:readium-lcp:$readium_version"

    //音视频播放器
    implementation 'com.gitee.CarGuo.GSYVideoPlayer:GSYVideoPlayer:v8.6.0-release-jitpack'

    //GIF支持库
    implementation 'pl.droidsonroids.gif:android-gif-drawable:1.2.25'

    //aurora-imui聊天界面
    implementation 'cn.jiguang.imui:messagelist:0.8.0'
    implementation 'cn.jiguang.imui:chatinput:0.10.0'
    implementation 'com.squareup.okhttp3:okhttp-sse:4.10.0'
    //显式引入okio，解决依赖冲突问题
    implementation 'com.squareup.okio:okio:3.3.0'

    //Markwon - Markdown解析和渲染库
    implementation 'io.noties.markwon:core:4.6.2'
    implementation 'io.noties.markwon:html:4.6.2'



}
//自动添加X-Library依赖
apply from: 'x-library.gradle'
//walle多渠道打包
apply from: 'multiple-channel.gradle'
apply plugin: 'org.jetbrains.kotlin.android'


